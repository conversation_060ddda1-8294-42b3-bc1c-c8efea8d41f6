{"name": "gamerji-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.9", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "js-cookie": "^3.0.5", "lucide-react": "^0.453.0", "next": "14.2.14", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.1", "react-otp-input": "^3.1.1", "react-redux": "^9.1.2", "react-slick": "^0.30.2", "react-toastify": "^10.0.6", "slick-carousel": "^1.8.1", "zod": "^3.23.8"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "eslint": "^8", "eslint-config-next": "14.2.14", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}